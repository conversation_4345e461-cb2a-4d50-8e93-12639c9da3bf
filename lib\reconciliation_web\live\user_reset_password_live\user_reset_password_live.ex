defmodule ReconciliationWeb.UserResetPasswordLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.Accounts

  def render(assigns) do
    ~H"""
    <style>
      :root {
        /* ProBASE Professional Color Scheme - Dark Slate Gray Theme */
        --primary-gradient: linear-gradient(135deg, #2f4f4f 0%, #3a5a5a 100%);
        --secondary-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        --accent-gradient: linear-gradient(135deg, #4a6a6a 0%, #3a5a5a 100%);

        /* Background Colors */
        --light-bg: #2f4f4f;
        --lighter-bg: #3a5a5a;
        --section-bg: #4a6a6a;
        --dark-bg: #253f3f;
        --darker-bg: #1a2f2f;
        --content-bg: #ffffff;

        /* Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.95);
        --glass-bg-dark: rgba(47, 79, 79, 0.95);
        --glass-border: rgba(100, 116, 139, 0.2);
        --glass-border-light: rgba(226, 232, 240, 0.8);

        /* Text Colors */
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-light: #9ca3af;

        /* ProBASE Brand Colors */
        --probase-primary: #2f4f4f;
        --probase-secondary: #f97316;
        --probase-accent: #4a6a6a;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: var(--primary-gradient);
        min-height: 100vh;
        overflow-x: hidden;
      }

      /* Background with animated gradient */
      .reset-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: var(--primary-gradient);
        z-index: -2;
      }

      .reset-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
          radial-gradient(circle at 20% 80%, rgba(249, 115, 22, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(74, 106, 106, 0.1) 0%, transparent 50%);
        animation: gradientShift 8s ease-in-out infinite;
      }

      @keyframes gradientShift {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
      }

      /* Main Container */
      .reset-container {
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        position: relative;
        z-index: 1;
        overflow: hidden;
      }

      /* Card Styling */
      .reset-card {
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        border: 1px solid var(--glass-border-light);
        border-radius: 16px;
        box-shadow: 0 15px 30px rgba(107, 114, 128, 0.15);
        padding: 1.5rem;
        width: 100%;
        max-width: 380px;
        max-height: 90vh;
        position: relative;
        overflow: hidden;
      }

      .reset-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--secondary-gradient);
        border-radius: 16px 16px 0 0;
      }

      /* Logo and Header */
      .reset-header {
        text-align: center;
        margin-bottom: 1.5rem;
      }

      .reset-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--probase-primary);
      }

      .reset-logo img {
        width: 28px;
        height: 28px;
        border-radius: 6px;
      }

      .reset-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--text-primary);
        margin: 0 0 0.25rem 0;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .reset-subtitle {
        color: var(--text-secondary);
        font-size: 0.8rem;
        margin: 0;
        line-height: 1.3;
      }

      /* Form Styling */
      .reset-form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .form-group {
        position: relative;
      }

      .form-label {
        display: block;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.3rem;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .form-input {
        width: 100%;
        padding: 0.75rem 0.875rem;
        border: 2px solid rgba(100, 116, 139, 0.2);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.8);
        font-size: 0.875rem;
        transition: all 0.3s ease;
        box-sizing: border-box;
      }

      .form-input:focus {
        outline: none;
        border-color: var(--probase-secondary);
        box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1);
        background: rgba(255, 255, 255, 0.95);
      }

      .form-input::placeholder {
        color: var(--text-secondary);
      }

      /* Override Phoenix Core Component Dark Theme Styling */
      .reset-form input[type="password"] {
        background: #ffffff !important;
        color: #374151 !important;
        border: 2px solid rgba(100, 116, 139, 0.2) !important;
        border-radius: 8px !important;
      }

      .reset-form input[type="password"]:focus {
        background: #ffffff !important;
        border-color: var(--probase-secondary) !important;
        box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1) !important;
      }

      /* Ensure all Phoenix input wrappers are white with border radius */
      .reset-form div[phx-feedback-for],
      .reset-card div[phx-feedback-for],
      .reset-form .phx-form-error,
      .reset-card .phx-form-error {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      /* Target any Phoenix wrapper divs and make them white */
      .reset-form div,
      .reset-card div {
        background-color: #ffffff !important;
        border-radius: inherit;
      }

      /* Specific Phoenix component wrappers */
      .reset-form .phx-input,
      .reset-card .phx-input,
      .reset-form [data-phx-component],
      .reset-card [data-phx-component] {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      /* Additional overrides for Tailwind classes */
      .reset-form .bg-gray-800,
      .reset-card .bg-gray-800 {
        background-color: #ffffff !important;
        border-radius: 8px !important;
      }

      .reset-form .text-white,
      .reset-card .text-white {
        color: #374151 !important;
      }

      /* Button Styling */
      .reset-button {
        width: 100%;
        padding: 0.75rem 1rem;
        background: var(--secondary-gradient);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 10px rgba(249, 115, 22, 0.3);
      }

      .reset-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(249, 115, 22, 0.4);
      }

      .reset-button:active {
        transform: translateY(0);
      }

      .reset-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      /* Footer Links */
      .reset-footer {
        text-align: center;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(100, 116, 139, 0.2);
      }

      .nav-links {
        color: var(--text-secondary);
        font-size: 0.75rem;
      }

      .nav-links a {
        color: var(--probase-secondary);
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .nav-links a:hover {
        color: var(--probase-secondary);
        text-decoration: underline;
      }

      .nav-links span {
        margin: 0 0.4rem;
        color: var(--text-light);
      }

      /* Error Styling */
      .error-message {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.3);
        color: #dc2626;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        margin-bottom: 1rem;
      }

      /* Responsive Design */
      @media (max-width: 640px) {
        .reset-container {
          padding: 0.75rem;
        }

        .reset-card {
          padding: 1.25rem;
          max-width: 320px;
          max-height: 85vh;
        }

        .reset-title {
          font-size: 1.1rem;
        }

        .reset-subtitle {
          font-size: 0.75rem;
        }

        .form-input {
          padding: 0.625rem 0.75rem;
          font-size: 0.8rem;
        }

        .reset-button {
          padding: 0.625rem 0.875rem;
          font-size: 0.8rem;
        }
      }
    </style>

    <div class="reset-bg"></div>

    <div class="reset-container">
      <div class="reset-card">
        <div class="reset-header">
          <div class="reset-logo">
            <img src="/images/probase-logo.png" alt="ProBASE Logo" />
            ProBASE
          </div>
          <h1 class="reset-title">Reset Password</h1>
          <p class="reset-subtitle">Enter your new password below</p>
        </div>

        <.simple_form for={@form} id="reset_password_form" phx-submit="reset_password" phx-change="validate" class="reset-form">
          <.error :if={@form.errors != []}>
            <div class="error-message">
              Oops, something went wrong! Please check the errors below.
            </div>
          </.error>

          <div class="form-group">
            <label for="user_password" class="form-label">New Password</label>
            <.input field={@form[:password]} type="password" placeholder="Enter new password" required class="form-input" />
          </div>

          <div class="form-group">
            <label for="user_password_confirmation" class="form-label">Confirm Password</label>
            <.input field={@form[:password_confirmation]} type="password" placeholder="Confirm new password" required class="form-input" />
          </div>

          <:actions>
            <.button phx-disable-with="Resetting..." class="reset-button">
              Reset Password
            </.button>
          </:actions>
        </.simple_form>

        <div class="reset-footer">
          <div class="nav-links">
            <.link href={~p"/users/register"}>Create Account</.link>
            <span>|</span>
            <.link href={~p"/users/log_in"}>Back to Sign In</.link>
          </div>
        </div>
      </div>
    </div>
    """
  end

  def mount(params, _session, socket) do
    socket = assign_user_and_token(socket, params)

    form_source =
      case socket.assigns do
        %{user: user} ->
          Accounts.change_user_password(user)

        _ ->
          %{}
      end

    {:ok, assign_form(socket, form_source) |> assign(page_title: "Reset Password"), temporary_assigns: [form: nil]}
  end

  # Do not log in the user after reset password to avoid a
  # leaked token giving the user access to the account.
  def handle_event("reset_password", %{"user" => user_params}, socket) do
    case Accounts.reset_user_password(socket.assigns.user, user_params) do
      {:ok, _} ->
        {:noreply,
         socket
         |> put_flash(:info, "✅ Password reset successfully! You can now log in with your new password.")
         |> push_event("show_toast", %{
           type: "success",
           title: "Password Reset Complete!",
           message: "Your password has been successfully reset. You can now log in with your new password.",
           duration: 5000
         })
         |> redirect(to: ~p"/users/log_in")}

      {:error, changeset} ->
        # Extract specific error messages from the changeset
        error_messages =
          changeset.errors
          |> Enum.map(fn {field, {message, _}} ->
            case field do
              :password -> "New password #{message}"
              :password_confirmation -> "Password confirmation #{message}"
              _ -> "#{field} #{message}"
            end
          end)
          |> Enum.join(", ")

        error_message = if error_messages != "", do: error_messages, else: "Password reset failed. Please check your input and try again."

        socket =
          socket
          |> put_flash(:error, "❌ Password reset failed: #{error_message}")
          |> push_event("show_toast", %{
            type: "error",
            title: "Password Reset Failed",
            message: error_message,
            duration: 6000
          })
          |> assign_form(Map.put(changeset, :action, :insert))

        {:noreply, socket}
    end
  end

  def handle_event("validate", %{"user" => user_params}, socket) do
    changeset = Accounts.change_user_password(socket.assigns.user, user_params)
    {:noreply, assign_form(socket, Map.put(changeset, :action, :validate))}
  end

  defp assign_user_and_token(socket, %{"token" => token}) do
    if user = Accounts.get_user_by_reset_password_token(token) do
      assign(socket, user: user, token: token)
    else
      socket
      |> put_flash(:error, "Reset password link is invalid or it has expired.")
      |> redirect(to: ~p"/")
    end
  end

  defp assign_form(socket, %{} = source) do
    assign(socket, :form, to_form(source, as: "user"))
  end
end
